'use client';

import { useState } from 'react';
import {
  Di<PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Clock,
  CheckCircle,
  Truck,
  Package,
  XCircle,
  User,
  Phone,
  Mail,
  MapPin,
  Calendar,
  DollarSign,
  FileText,
} from 'lucide-react';

interface Order {
  id: string;
  customer_id: string;
  customer_name: string;
  customer_email: string;
  status:
    | 'pending'
    | 'confirmed'
    | 'in-transit'
    | 'pending-admin-confirmation'
    | 'delivered'
    | 'closed'
    | 'cancelled';
  pickup_address: {
    street_address: string;
    city: string;
    state: string;
    contact_name: string;
    contact_phone: string;
  };
  delivery_addresses: {
    street_address: string;
    city: string;
    state: string;
    recipient_name: string;
    phone: string;
  };
  package_details: {
    description: string;
    weight: string;
    dimensions: string;
    value: string;
  };
  total_cost: number;
  payment_status: 'pending' | 'paid' | 'failed';
  created_at: string;
  updated_at: string;
}

interface OrderDetailsModalProps {
  order: Order | null;
  isOpen: boolean;
  onClose: () => void;
  onUpdateStatus?: (orderId: string, newStatus: Order['status']) => void;
}

const statusConfig = {
  pending: {
    label: 'Pendiente',
    color: 'bg-yellow-100 text-yellow-800',
    icon: <Clock className='w-4 h-4' />,
  },
  confirmed: {
    label: 'Confirmado',
    color: 'bg-blue-100 text-blue-800',
    icon: <CheckCircle className='w-4 h-4' />,
  },
  'in-transit': {
    label: 'En Tránsito',
    color: 'bg-orange-100 text-orange-800',
    icon: <Truck className='w-4 h-4' />,
  },
  'pending-admin-confirmation': {
    label: 'Pendiente Confirmación',
    color: 'bg-orange-100 text-orange-800',
    icon: <Package className='w-4 h-4' />,
  },
  delivered: {
    label: 'Entregado',
    color: 'bg-green-100 text-green-800',
    icon: <Package className='w-4 h-4' />,
  },
  closed: {
    label: 'Cerrado',
    color: 'bg-gray-100 text-gray-800',
    icon: <CheckCircle className='w-4 h-4' />,
  },
  cancelled: {
    label: 'Cancelado',
    color: 'bg-red-100 text-red-800',
    icon: <XCircle className='w-4 h-4' />,
  },
};

const paymentStatusConfig = {
  pending: { label: 'Pago Pendiente', color: 'bg-yellow-100 text-yellow-800' },
  paid: { label: 'Pagado', color: 'bg-green-100 text-green-800' },
  failed: { label: 'Pago Fallido', color: 'bg-red-100 text-red-800' },
};

export function OrderDetailsModal({
  order,
  isOpen,
  onClose,
  onUpdateStatus,
}: OrderDetailsModalProps) {
  const [updatingStatus, setUpdatingStatus] = useState(false);

  if (!order) return null;

  const statusConf = statusConfig[order.status];
  const paymentConf = paymentStatusConfig[order.payment_status];

  const handleStatusUpdate = async (newStatus: Order['status']) => {
    if (!onUpdateStatus) return;

    setUpdatingStatus(true);
    try {
      await onUpdateStatus(order.id, newStatus);
    } finally {
      setUpdatingStatus(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-w-4xl max-h-[90vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Package className='w-5 h-5' />
            Pedido #{order.id.slice(-8)}
          </DialogTitle>
          <DialogDescription>
            Detalles completos del pedido y información del cliente
          </DialogDescription>
        </DialogHeader>

        <div className='space-y-6'>
          {/* Order Status & Payment */}
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-4'>
              <Badge className={statusConf.color}>
                <span className='mr-1'>{statusConf.icon}</span>
                {statusConf.label}
              </Badge>
              <Badge className={paymentConf.color}>{paymentConf.label}</Badge>
            </div>
          </div>

          {/* Customer Information */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <User className='w-4 h-4' />
                Información del Cliente
              </CardTitle>
            </CardHeader>
            <CardContent className='space-y-3'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div>
                  <p className='text-sm font-medium text-gray-600'>Nombre</p>
                  <p className='text-sm'>{order.customer_name}</p>
                </div>
                <div>
                  <p className='text-sm font-medium text-gray-600'>Email</p>
                  <p className='text-sm flex items-center gap-1'>
                    <Mail className='w-3 h-3' />
                    {order.customer_email}
                  </p>
                </div>
                <div>
                  <p className='text-sm font-medium text-gray-600'>Teléfono</p>
                  <p className='text-sm flex items-center gap-1'>
                    <Phone className='w-3 h-3' />
                    {order.pickup_address.contact_phone}
                  </p>
                </div>
                <div>
                  <p className='text-sm font-medium text-gray-600'>
                    ID del Cliente
                  </p>
                  <p className='text-sm font-mono text-gray-500'>
                    {order.customer_id.slice(-8)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Pickup Address */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <MapPin className='w-4 h-4' />
                Dirección de Recogida
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className='space-y-2'>
                <p className='font-medium'>
                  {order.pickup_address.contact_name}
                </p>
                <p className='text-sm text-gray-600'>
                  {order.pickup_address.street_address}
                </p>
                <p className='text-sm text-gray-600'>
                  {order.pickup_address.city}, {order.pickup_address.state}
                </p>
                <p className='text-sm text-gray-600 flex items-center gap-1'>
                  <Phone className='w-3 h-3' />
                  {order.pickup_address.contact_phone}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Delivery Address */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <Truck className='w-4 h-4' />
                Dirección de Entrega
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className='space-y-2'>
                <p className='font-medium'>
                  {order.delivery_addresses.recipient_name}
                </p>
                <p className='text-sm text-gray-600'>
                  {order.delivery_addresses.street_address}
                </p>
                <p className='text-sm text-gray-600'>
                  {order.delivery_addresses.city},{' '}
                  {order.delivery_addresses.state}
                </p>
                <p className='text-sm text-gray-600 flex items-center gap-1'>
                  <Phone className='w-3 h-3' />
                  {order.delivery_addresses.phone}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Package Details */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <Package className='w-4 h-4' />
                Detalles del Paquete
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
                <div>
                  <p className='text-sm font-medium text-gray-600'>
                    Descripción
                  </p>
                  <p className='text-sm'>{order.package_details.description}</p>
                </div>
                <div>
                  <p className='text-sm font-medium text-gray-600'>Peso</p>
                  <p className='text-sm'>{order.package_details.weight}</p>
                </div>
                <div>
                  <p className='text-sm font-medium text-gray-600'>
                    Dimensiones
                  </p>
                  <p className='text-sm'>{order.package_details.dimensions}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Order Information */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <FileText className='w-4 h-4' />
                Información del Pedido
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div>
                  <p className='text-sm font-medium text-gray-600'>Total</p>
                  <p className='text-lg font-bold text-green-600 flex items-center gap-1'>
                    <DollarSign className='w-4 h-4' />$
                    {order.total_cost?.toFixed(2) || '0.00'} MXN
                  </p>
                </div>
                <div>
                  <p className='text-sm font-medium text-gray-600'>
                    Fecha de Creación
                  </p>
                  <p className='text-sm flex items-center gap-1'>
                    <Calendar className='w-3 h-3' />
                    {new Date(order.created_at).toLocaleDateString('es-MX', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit',
                    })}
                  </p>
                </div>
                <div>
                  <p className='text-sm font-medium text-gray-600'>
                    Última Actualización
                  </p>
                  <p className='text-sm flex items-center gap-1'>
                    <Clock className='w-3 h-3' />
                    {new Date(order.updated_at).toLocaleDateString('es-MX', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit',
                    })}
                  </p>
                </div>
                <div>
                  <p className='text-sm font-medium text-gray-600'>
                    ID del Pedido
                  </p>
                  <p className='text-sm font-mono text-gray-500'>{order.id}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Status Update */}
          {onUpdateStatus && (
            <Card>
              <CardHeader>
                <CardTitle>Actualizar Estado del Pedido</CardTitle>
              </CardHeader>
              <CardContent>
                <div className='flex items-center gap-4'>
                  <p className='text-sm text-gray-600'>Nuevo estado:</p>
                  <select
                    className='border rounded-md px-3 py-2 text-sm'
                    onChange={e =>
                      handleStatusUpdate(e.target.value as Order['status'])
                    }
                    disabled={updatingStatus}
                    defaultValue={order.status}
                  >
                    <option value='pending'>Pendiente</option>
                    <option value='confirmed'>Confirmado</option>
                    <option value='in-transit'>En Tránsito</option>
                    <option value='pending-admin-confirmation'>
                      Pendiente Confirmación
                    </option>
                    <option value='delivered'>Entregado</option>
                    <option value='cancelled'>Cancelado</option>
                  </select>
                  {updatingStatus && (
                    <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600'></div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Footer */}
          <div className='flex justify-end pt-4 border-t'>
            <Button variant='outline' onClick={onClose}>
              Cerrar
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
