export interface ProductItem {
  id: string;
  name: string;
  quantity: number;
  unit_measure: string;
  unit_price: number;
  subtotal: number;
  notes?: string;
}

export interface DeliveryAddress {
  id: string;
  street: string;
  number?: string;
  colony: string;
  city: string;
  state: string;
  zip: string;
  references?: string;
}

export interface OrderFormData {
  // Customer Information
  customer_name: string;
  customer_phone: string;
  customer_email?: string;

  // Delivery Address
  delivery_address: DeliveryAddress;

  // Shopping Cart
  products: ProductItem[];

  // Delivery Options
  delivery_date: string;
  delivery_time_slot: string;
  delivery_mode: 'home' | 'store';

  // Payment
  payment_method: 'card' | 'cash' | 'digital_wallet' | 'bank_transfer';
  invoice_required: boolean;

  // Additional Notes
  special_instructions?: string;
  allow_substitutions: boolean;
}

export interface OrderFormProps {
  initialData?: OrderFormData;
  onSubmit: (_data: OrderFormData) => void;
  onCancel?: () => void;
  loading?: boolean;
}
